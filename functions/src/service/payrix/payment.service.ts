import { AxiosError } from "axios";
import { logger } from "../../helpers/logger.js";
import { TokenPaymentData, PaymentResult, TokenDeletionResult, PayrixError } from "../../types/payrix.types.js";
import { createPayrixApiClient, PAYRIX_API_URL } from "./api-client.js";

const apiClient = createPayrixApiClient();

export async function processTokenPayment(paymentData: TokenPaymentData): Promise<PaymentResult> {
  try {
    logger.info("Processing token payment", {
      merchantId: paymentData.merchantId,
      token: paymentData.token.substring(0, 8) + "...",
      amount: paymentData.amount,
      description: paymentData.description,
    });

    const transactionData = {
      merchant: paymentData.merchantId,
      type: "1",
      origin: "2",
      token: paymentData.token,
      total: paymentData.amount.toString(),
      description: paymentData.description || "Token-based payment",
      ...(paymentData.customerInfo?.email && { email: paymentData.customerInfo.email }),
      ...(paymentData.customerInfo?.name && { name: paymentData.customerInfo.name }),
      ...(paymentData.customerInfo?.address && {
        address1: paymentData.customerInfo.address.line1,
        address2: paymentData.customerInfo.address.line2,
        city: paymentData.customerInfo.address.city,
        state: paymentData.customerInfo.address.state,
        zip: paymentData.customerInfo.address.zip,
        country: paymentData.customerInfo.address.country,
      }),
    };

    logger.info("Transaction data prepared", {
      merchantId: paymentData.merchantId,
      transactionType: "sale (token-based payment)",
      type: transactionData.type,
      total: transactionData.total,
      amount: paymentData.amount,
    });

    logger.info("Sending transaction request to Payrix", {
      url: `${PAYRIX_API_URL}/txns`,
      merchantId: paymentData.merchantId,
      amount: paymentData.amount,
      token: paymentData.token.substring(0, 8) + "...",
    });

    const response = await apiClient.post("/txns", transactionData);

    logger.info("Token payment response received", {
      status: response.status,
      responseStructure: {
        hasData: !!response.data,
        hasResponse: !!response.data?.response,
        hasResponseData: !!response.data?.response?.data,
        responseDataLength: Array.isArray(response.data?.response?.data) ? response.data.response.data.length : 0,
        hasErrors: !!response.data?.response?.errors,
        errorsLength: Array.isArray(response.data?.response?.errors) ? response.data.response.errors.length : 0,
      },
      fullResponse: response.data,
    });

    let transactionData_response;

    if (response.data?.response?.data?.[0]) {
      transactionData_response = response.data.response.data[0];
      logger.info("Found transaction data in standard structure", {
        transactionId: transactionData_response.id,
        status: transactionData_response.status,
      });
    } else if (response.data?.data?.[0]) {
      transactionData_response = response.data.data[0];
      logger.info("Found transaction data in alternative structure", {
        transactionId: transactionData_response.id,
        status: transactionData_response.status,
      });
    } else if (response.data?.id) {
      transactionData_response = response.data;
      logger.info("Found transaction data in direct structure", {
        transactionId: transactionData_response.id,
        status: transactionData_response.status,
      });
    }

    const errors = response.data?.response?.errors || response.data?.errors;
    if (errors && Array.isArray(errors) && errors.length > 0) {
      logger.error("Payrix API returned errors", {
        errors,
        amount: paymentData.amount,
        merchantId: paymentData.merchantId,
      });
      const errorMessages = errors.map((err: PayrixError) => `${err.field || "general"}: ${err.msg}`).join(", ");
      throw new Error(`Payrix API errors: ${errorMessages}`);
    }

    if (!transactionData_response) {
      logger.error("No transaction data found in any expected structure", {
        responseData: response.data,
        amount: paymentData.amount,
        hasErrors: !!errors,
        errorsCount: Array.isArray(errors) ? errors.length : 0,
        responseStatus: response.status,
      });
      throw new Error("Invalid Payrix response structure: no transaction data found");
    }

    return {
      success: true,
      transaction: transactionData_response,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    logger.error("Token payment processing error", {
      merchantId: paymentData.merchantId,
      token: paymentData.token.substring(0, 8) + "...",
      status: axiosError.response?.status,
      statusText: axiosError.response?.statusText,
      data: axiosError.response?.data,
      message: axiosError.message,
    });

    const errorData = axiosError.response?.data as { message?: string } | undefined;

    return {
      success: false,
      error: `Payment processing failed: ${errorData?.message || axiosError.message}`,
    };
  }
}

export async function cleanupToken(token: string): Promise<TokenDeletionResult> {
  try {
    logger.info("Cleaning up token for PCI compliance", {
      token: token.substring(0, 8) + "...",
    });

    const response = await apiClient.delete(`/tokens/${token}`);

    logger.info("Token cleanup successful", {
      token: token.substring(0, 8) + "...",
      status: response.status,
    });

    return {
      success: true,
      message: "Token deleted successfully",
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    logger.warn("Token cleanup failed (non-critical)", {
      token: token.substring(0, 8) + "...",
      status: axiosError.response?.status,
      statusText: axiosError.response?.statusText,
      message: axiosError.message,
      note: "Token may have already expired or been deleted",
    });

    return {
      success: false,
      error: `Token cleanup failed: ${axiosError.message}`,
    };
  }
}

